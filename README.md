## 项目背景
驾考私教业务需要一套完善的在线教学系统，以满足以下需求：  
实时音视频互动: 讲师与学员能够进行实时音视频通话，模拟线下教学场景。  
互动白板: 讲师可以通过白板进行板书、演示课件、以及做题等操作，学员可以实时查看并参与随堂练习。  
课程管理: 讲师可以管理课程内容、布置课后作业、发起随堂测验等。  
学员信息管理: 讲师可以查看学员的学习进度、练习情况、考试结果等。

## 项目概述
本项目旨在为驾考私教业务开发一个在线一对一教学系统，支持讲师与学员之间的实时互动教学、课程管理、课后作业布置与批改等功能。系统通过集成腾讯云的实时音视频（TRTC）、即时通信（IM）和互动白板技术，实现了高效、稳定的在线教学体验。

## 项目的术语定义
授课工具  
子项目名称，一对一教学系统有2个子项目共同提供完整的授课功能，本项目为授课工具，提供 即时通信+互动白板+学员信息展示等功能；  
电子教案  
另外一个子项目名称，为教案管理系统，提供做题+课件页面（供讲师嵌入互动白板进行授课）+课程管理。  
白板推流  
互动白板是腾讯云提供的一种在线白板工具，利用TRTC+IM+白板三种sdk，IM把白板的操作同步到云主机中，云主机把白板的内容进行录制，再用TRTC把录制的视频实时推送给学员。

## 项目的部署
授课工具：https://laofuzi.kakamobi.com/personal-training-live/  
电子教案：https://laofuzi.kakamobi.com/personal-training-management/  
git：  
https://git.mucang.cn/jiakaobaodian-webfront/personal-training-management

## 项目的运行环境
运行在PC端、驾考宝典APP、微信浏览器

## 项目的技术选型
Vue3+Vuetify+Vite

## 第三方SDK
腾讯云TRT  
腾讯云IM  
腾讯云互动白板  

## 项目的功能
1. 学员档案  
讲师可以实时查看学员的练习和考试情况，了解学员的学习进度和知识掌握情况。包含基础信息、错题本、考试记录等，支持车型科目切换

2. 随堂测验  
讲师可以发起随堂测验，学员端会弹出答题弹窗。  
测验结束后，系统会统计学员的答题情况，包括答对题数、答错题数等，并生成测验结果。  
讲师可以查看学员的测验结果，并根据结果进行针对性讲解和辅导。

3. 用户管理  
成员管理：讲师可以查看和管理课堂成员，例如禁言、禁音等操作。  
角色区分：系统区分讲师、学员、督导等不同角色，并赋予不同的权限和功能。  

4. 多终端支持  
讲师端在PC端使用  
学员端在移动端使用

5. 聊天快捷回复  
学员端提供快捷回复功能，方便学员快速回复习题答案

6. 白板功能
画笔
鼠标
矩形
橡皮擦
文本
一键清空

7. 教案
#### 错题
    1、专项/知识点错题
    2、近十场考试错题
    3、单次模拟考试错题
    4、所有试题
    5、专项试题
    6、随堂测验结果
#### 课件
    1、专项课件
    2、考前冲刺课件

## 目录结构
```
d:\mucang\personal-training-live
├── .git/
├── .gitignore
├── .gitlab-ci.yml
├── .vscode/
├── README.md
├── index.html
├── package.json
├── src/
│   ├── assets/
│   ├── components/
│   │   ├── Board/                # 白板相关组件
│   │   │   ├── Main.vue          # 白板主组件
│   │   │   ├── Sketch.vue        # 白板绘制组件
│   │   │   ├── Toolbar.vue       # 白板工具栏组件
│   │   │   └── Bottombar.vue     # 白板底部栏组件
│   │   ├── ChatEditor/           # 聊天框组件
│   │   ├── Main/                 # 主页面组件
│   │   ├── MessageList/          # 消息列表组件
│   │   ├── TUI/                  # 公共UI组件
│   │   ├── AudioControl.vue      # 音频控制组件
│   │   ├── CourseContent.vue     # 课程内容展示组件
│   │   ├── Device.vue            # 设备管理组件
│   │   ├── ExamDialog.vue        # 考试弹窗组件
│   │   ├── ExamResultDialog.vue  # 考试结果弹窗组件
│   │   ├── Footer.vue            # 底部栏组件
│   │   ├── FullScreenControl.vue # 全屏控制组件
│   │   ├── Header.vue            # 头部组件
│   │   ├── IframeWrap.vue        # iframe封装组件
│   │   ├── Player.vue            # 音视频播放器组件
│   │   ├── PublicMaterial.vue    # 资料库组件
│   │   ├── Shortcut.vue          # 快捷键设置组件
│   │   ├── StudentDetail.vue     # 学生档案组件
│   │   ├── TeachPlan.vue         # 教案组件
│   │   ├── UserList.vue          # 用户列表组件
│   │   └── WakeupBtn.vue         # 唤醒按钮组件
│   ├── hooks/
│   ├── libs/
│   ├── router/
│   ├── services/
│   ├── store/
│   ├── types/
│   ├── utils/
│   ├── views/
│   ├── App.vue
│   ├── main.ts
│   ├── style.scss
│   └── vite-env.d.ts
├── static/
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.mts
```

## 项目的逻辑视图

1、**实时互动**

![](https://jiakao-web.mc-cdn.cn/jiakao-web/2025/03/28/20/1e2b527b44ca435787e9a9366ee60422.png)