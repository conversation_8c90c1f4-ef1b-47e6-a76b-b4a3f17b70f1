import { onMounted, watch, getCurrentInstance, ComponentInternalInstance } from 'vue';
import { URLParams, getAuthToken, goLogin, toAwait, httpRequest } from '@/utils/utils';
import TIM, { timInstance } from '@/services/tim';
import {Message} from 'tim-js-sdk'
import TUIMessageBox from '@/components/TUI/MessageBox/index';
import TUIMessage from '@/components/TUI/Message/index';
import useBasicStore from '@/store/basic';
import useRoomStore from '@/store/room';
import useBoardStore from '@/store/board';
import useChatStore from '@/store/chat';
import {OPERATETYPE} from '@/types/type'
import {SAU_LISTENER} from '@/utils/constant'

export default function () {
  const chatStore = useChatStore();
  async function getUserSig() {
    const authToken = await getAuthToken()
    if (!authToken) {
      await goLogin()
    }
    const [err, res] = await toAwait(httpRequest({
      url: 'api/web/teaching-room/auth.htm',
      params: {
        roomNo: URLParams.roomNo
      },
      host: 'parrot',
    }))
    if (err && err.errorCode === 403) {
      await goLogin()
      return getUserSig()
    }
    // const currentUserInfo = JSON.parse(sessionStorage.getItem('personal-training-basicInfo') as string);
    // if (currentUserInfo?.userId && false) {
    //   return currentUserInfo;
    // }
    return [err, res];
  }

  async function getRoomInfo() {
    return httpRequest({
      url: 'api/web/teaching-room/get-teaching-room-info.htm',
      params: {
        roomNo: URLParams.roomNo
      },
      host: 'parrot',
    })
  }
  async function createRoom(subject: string) {
    return httpRequest({
      url: 'api/web/teaching-room/create-teaching-room.htm',
      params: {
        name: subject,
        roomNo: URLParams.roomNo
      },
      host: 'parrot',
    })
  }
  async function getAndCreateRoom(subject: string, isMaster: boolean) {
    const [, res] = await toAwait(getRoomInfo())
    if (!res?.roomId) {
      if (isMaster) {
        await createRoom(subject)
        return getRoomInfo()
      } else {
        TUIMessage({
          type: 'error',
          message: '课程还未开始，请稍后再来',
          duration: 3000,
          appContext
        })
        return Promise.reject()
      }
    }
    return res
  }
  async function getCourse() {
    return httpRequest({
      url: 'api/web/course/get-course.htm',
      params: {
        courseId: URLParams.courseId
      },
      host: 'parrot',
    })
  }

  // init input params
  const { appContext } = getCurrentInstance() as ComponentInternalInstance
  const basicStore = useBasicStore();
  const roomStore = useRoomStore();
  const boardStore = useBoardStore();

  onMounted(async () => {
    const [err, res] = await getUserSig()
    if (err) {
      TUIMessage({
        type: 'error',
        message: err.message,
        duration: 0,
        appContext
      })
      return
    }
    const {
      sdkAppId,
      trtcUserId: userId,
      trtcUserSig: userSig,
      nickName: userName,
      avatar,
      userType,
      whiteBoardUserId: whUserId,
      whiteBoardUserSig: whUserSig,
    } = res
    const isMaster = userType === 1
    const {subject, sno, endTime, serverTime} = await getCourse()
    const localTime = +new Date()
    const {roomId, status} = await getAndCreateRoom(subject, isMaster)
    const roomInfo = {sdkAppId, userId, userSig, roomId: String(roomId), roomNo: URLParams.roomNo, userName, avatar, subject, sno, endTime, serverTime, localTime}
    roomStore.$patch({isMaster, isSauListener: userId === SAU_LISTENER, status});
    boardStore.$patch({whUserId, whUserSig})
    basicStore.$patch(roomInfo);
  })


  watch(
    () => chatStore.isTimReady,
    (val) => {
      if (val) {
        timInstance.on(TIM.EVENT.MESSAGE_RECEIVED, (event: { data: Message[] }) => {
          const messages = event.data;

          messages.forEach(async (message) => {
            if (message.type === TIM.TYPES.MSG_CUSTOM) {
              const { payload: {data} } = message;
              const messageData = JSON.parse(data || '{}')
              const {operateType} = messageData
              // if (basicStore.userId === userID) {
                if (operateType === OPERATETYPE.ROOMSTATUSUPDATE) {
                  setTimeout(async () => {
                    const {status} = await getRoomInfo();
                    roomStore.$patch({ status});
                  }, 1000)
                } else if (operateType === OPERATETYPE.DISSMISSBYADMIN) {
                  TUIMessageBox({
                    title: '提示',
                    message: '管理员已经解散了会议室',
                    confirmButtonText: '确定',
                    appContext
                  });
                  const {status} = await getRoomInfo();
                  roomStore.$patch({ status});
                }
              // }
            }
          });
        });
      }
    }, {
      immediate: true
    }
  )
};
